import {FC, Fragment, memo, useCallback} from 'react';
import {signOut} from 'next-auth/react';
import {cls, isDev} from '@core/helpers';
import {useStore, useCart, useCustomer, useTrans, useUI} from '@core/hooks';
import {UiAvatar, UiLink, UiMenu, UiTransition} from '@core/components/ui';
import {BagIcon} from '@core/icons/solid';
import dynamic from 'next/dynamic';
import {Cookies} from 'react-cookie-consent';
import ChangeCustomer from '@components/common/ChangeCustomer';

const MiniCart = dynamic(() => import('@components/common/MiniCart'));

const HeaderUserNavPartial: FC = memo(() => {
    const t = useTrans();
    const store = useStore();
    const {openSideBar, openModal} = useUI();
    const customer = useCustomer();
    const {itemCount: cartItemsCount} = useCart();

    const onOpenMiniCart = useCallback(() => {
        openSideBar(t('My Cart'), <MiniCart />);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const onOpenCustomerChange = useCallback(() => {
        openModal(t('Change Customer'), <ChangeCustomer />);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    console.log('customer', store.paymentOnly);

    return (
        <nav className="flex items-center justify-end gap-0.5">
            {customer?.erpUserId && (
                <p className="mr-3.5 cursor-default font-medium">
                    {customer.name}
                </p>
            )}

            {!store.paymentOnly && (
                <button
                    className="group relative flex cursor-pointer items-center rounded-lg px-4 py-2.5 outline-none transition hover:bg-secondary-100 focus:outline-none active:outline-none"
                    onClick={onOpenMiniCart}
                >
                    <BagIcon className="h-6 w-6 text-gray-700" />

                    <div className="ml-2 whitespace-nowrap">{t('My Cart')}</div>
                    <span
                        className="absolute left-7 top-0.5 inline-flex items-center justify-center rounded-full bg-primary-600 text-[10px] font-bold text-white"
                        style={{
                            paddingLeft: '2px',
                            paddingRight: '2px',
                            minWidth: '1.125rem',
                            minHeight: '1.125rem'
                        }}
                    >
                        {cartItemsCount}
                    </span>
                </button>
            )}

            <UiMenu as="div" className="relative inline-block">
                {({open}) => (
                    <>
                        <UiMenu.Button
                            as="div"
                            aria-label={customer?.name}
                            className={cls(
                                'group relative flex cursor-pointer items-center rounded-lg px-4 py-2 outline-none transition hover:bg-secondary-100 focus:outline-none active:outline-none',
                                {'bg-secondary-100': open}
                            )}
                        >
                            <UiAvatar
                                className="cursor-pointer bg-gray-700 text-white"
                                name={customer?.name}
                                size="md"
                            />

                            <div className="ml-2 whitespace-nowrap">
                                {t('My Account')}
                            </div>
                        </UiMenu.Button>

                        <UiTransition
                            show={open}
                            as={Fragment}
                            enter="transition"
                            enterFrom="transform opacity-0"
                            enterTo="transform opacity-100"
                            leave="transition"
                            leaveFrom="transform opacity-100"
                            leaveTo="transform opacity-0"
                        >
                            <UiMenu.Items
                                className="card-container absolute right-2 z-dropdown mt-4 min-w-max origin-top-right bg-white text-sm outline-none"
                                style={{minWidth: '14rem'}}
                            >
                                <div role="group">
                                    <div className="m-3">
                                        <div className="font-medium">
                                            {customer?.name}
                                        </div>
                                        <div className="text-muted">
                                            {customer?.email}
                                        </div>
                                    </div>

                                    <div className="h-[1px] w-full bg-secondary-400"></div>

                                    {customer?.erpUserId && (
                                        <UiMenu.Item>
                                            <button
                                                className="flex w-full p-3 transition hover:bg-secondary-100"
                                                onClick={onOpenCustomerChange}
                                            >
                                                {t('Change Customer')}
                                            </button>
                                        </UiMenu.Item>
                                    )}

                                    {!store.paymentOnly && (
                                        <>
                                            <UiMenu.Item>
                                                <UiLink
                                                    href="/account/my-orders"
                                                    className="flex p-3 transition hover:bg-secondary-100"
                                                >
                                                    {t('My Orders')}
                                                </UiLink>
                                            </UiMenu.Item>

                                            <UiMenu.Item>
                                                <UiLink
                                                    href="/account/my-favorites"
                                                    className="flex p-3 transition hover:bg-secondary-100"
                                                >
                                                    {t('My Favorites')}
                                                </UiLink>
                                            </UiMenu.Item>

                                            <UiMenu.Item>
                                                <UiLink
                                                    href="/account/my-collections"
                                                    className="flex p-3 transition hover:bg-secondary-100"
                                                >
                                                    {t('My Collections')}
                                                </UiLink>
                                            </UiMenu.Item>

                                            <UiMenu.Item>
                                                <UiLink
                                                    href="/account/my-reviews"
                                                    className="flex p-3 transition hover:bg-secondary-100"
                                                >
                                                    {t('My Reviews')}
                                                </UiLink>
                                            </UiMenu.Item>
                                        </>
                                    )}

                                    <UiMenu.Item>
                                        <UiLink
                                            href="/account/my-addresses"
                                            className="flex p-3 transition hover:bg-secondary-100"
                                        >
                                            {t('My Addresses')}
                                        </UiLink>
                                    </UiMenu.Item>

                                    <UiMenu.Item>
                                        <UiLink
                                            href="/account/my-account"
                                            className="flex p-3 transition hover:bg-secondary-100"
                                        >
                                            {t('My Account')}
                                        </UiLink>
                                    </UiMenu.Item>

                                    <UiMenu.Item>
                                        <button
                                            className="flex w-full items-center rounded-b-lg p-3 transition hover:bg-secondary-100"
                                            onClick={() => {
                                                Cookies.remove('cart-id');
                                                signOut();
                                            }}
                                        >
                                            {t('Sign Out')}
                                        </button>
                                    </UiMenu.Item>
                                </div>
                            </UiMenu.Items>
                        </UiTransition>
                    </>
                )}
            </UiMenu>
        </nav>
    );
});

if (isDev) {
    HeaderUserNavPartial.displayName = 'HeaderUserNavPartial';
}

export default HeaderUserNavPartial;
